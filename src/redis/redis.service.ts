import { Injectable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { Redis } from 'ioredis';
import {ClosingError,GlideClusterClient } from "@valkey/valkey-glide";

@Injectable()
export class RedisService implements OnModuleInit {
  private readonly logger = new Logger(RedisService.name);
  private redisClient;

  constructor() {

  }

  async onModuleInit() {
    try {
      const redisHost = process.env.REDIS_HOST;
      this.redisClient = await GlideClusterClient.createClient({
        addresses: [{ host: redisHost, port: 6379 }],
        useTLS: true
      });

      // Perform PING operation
      const pong = await this.redisClient.ping();
      this.logger.log(`Redis connection successful. PING response: ${pong}`);
      this.logger.log('Redis cluster client initialized');
    } catch (error) {
      this.logger.error('Failed to connect to Redis cluster', error);
    }
  }

  async get(key: string): Promise<string | null> {
    try {
      const value = await this.redisClient.get(key);
      return value;
    } catch (error) {
      this.logger.error(`<PERSON><PERSON><PERSON> getting key ${key} from Redis`, error);
      return null;
    }
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    try {
      if (ttlSeconds) {
        await this.redisClient.set(key, value, 'EX', ttlSeconds);
      } else {
        await this.redisClient.set(key, value);
      }
    } catch (error) {
      this.logger.error(`Error setting key ${key} in Redis`, error);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redisClient.del(key);
    } catch (error) {
      this.logger.error(`Error deleting key ${key} from Redis`, error);
    }
  }
}
